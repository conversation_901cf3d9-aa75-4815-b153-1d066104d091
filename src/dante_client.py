"""
Dante API Client for earthquake data from INGV
"""
import httpx
from typing import Optional, Dict, Any, List
from datetime import datetime
import json


class DanteAPIClient:
    """Client for interacting with the Dante earthquake API"""
    
    def __init__(self, base_url: str = "https://caravel-dante.int.ingv.it/api"):
        self.base_url = base_url.rstrip('/')
        self.token: Optional[str] = None
        self.client = httpx.Client(timeout=30.0)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.client.close()
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers
    
    def login(self, name: str, email: str, password: str) -> Dict[str, Any]:
        """
        Authenticate with the Dante API
        
        Args:
            name: User name
            email: User email
            password: User password
            
        Returns:
            Authentication response with token
        """
        url = f"{self.base_url}/login"
        data = {
            "name": name,
            "email": email,
            "password": password
        }
        
        response = self.client.post(url, json=data, headers=self._get_headers())
        response.raise_for_status()
        
        result = response.json()
        if "token" in result:
            self.token = result["token"]
        
        return result
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get API status
        
        Returns:
            Status information
        """
        url = f"{self.base_url}/status"
        response = self.client.get(url, headers=self._get_headers())
        response.raise_for_status()
        return response.json()
    
    def get_events_group(self, **params) -> Dict[str, Any]:
        """
        Get clustered events with preferred origin and magnitude
        
        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)
            
        Returns:
            Events group data
        """
        url = f"{self.base_url}/quakedb/v1/events-group"
        response = self.client.get(url, params=params, headers=self._get_headers())
        response.raise_for_status()
        return response.json()
    
    def get_events(self, **params) -> Dict[str, Any]:
        """
        Get events with preferred origin and magnitude from same instance
        
        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)
            
        Returns:
            Events data
        """
        url = f"{self.base_url}/quakedb/v1/events"
        response = self.client.get(url, params=params, headers=self._get_headers())
        response.raise_for_status()
        return response.json()
    
    def get_origins(self, **params) -> Dict[str, Any]:
        """
        Get earthquake origins
        
        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)
            
        Returns:
            Origins data
        """
        url = f"{self.base_url}/quakedb/v1/origins"
        response = self.client.get(url, params=params, headers=self._get_headers())
        response.raise_for_status()
        return response.json()
    
    def get_magnitudes(self, **params) -> Dict[str, Any]:
        """
        Get magnitude data
        
        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)
            
        Returns:
            Magnitudes data
        """
        url = f"{self.base_url}/quakedb/v1/magnitudes"
        response = self.client.get(url, params=params, headers=self._get_headers())
        response.raise_for_status()
        return response.json()
    
    def get_all_data(self, **params) -> Dict[str, Any]:
        """
        Get all earthquake data (origins with all magnitudes)
        
        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)
            
        Returns:
            All earthquake data
        """
        url = f"{self.base_url}/quakedb/v1/all"
        response = self.client.get(url, params=params, headers=self._get_headers())
        response.raise_for_status()
        return response.json()
